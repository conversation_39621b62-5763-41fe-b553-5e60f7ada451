<?php

declare(strict_types=1);

namespace App\Console\Commands;

use App\Models\StudentEnrollment;
use App\Services\GeneralSettingsService;
use App\Services\BrowsershotService;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;

class GenerateConcurrentAssessmentCommand extends Command
{
    /**
     * The name and signature of the console command.
     */
    protected $signature = 'assessment:generate-concurrent {enrollment_id : The enrollment ID} {output_dir : Output directory} {job_id : Job ID for tracking}';

    /**
     * The console command description.
     */
    protected $description = 'Generate assessment PDF for concurrent bulk processing';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        try {
            $enrollmentId = (int) $this->argument('enrollment_id');
            $outputDir = $this->argument('output_dir');
            $jobId = $this->argument('job_id');
            
            // Find the enrollment record (including soft-deleted)
            $enrollment = StudentEnrollment::withTrashed()
                ->with(['student', 'course', 'subjectsEnrolled.subject', 'studentTuition'])
                ->find($enrollmentId);

            if (!$enrollment) {
                $this->error("Enrollment with ID {$enrollmentId} not found.");
                return 1;
            }

            // Generate the assessment PDF
            $pdfPath = $this->generateAssessmentPdf($enrollment, $outputDir, $jobId);

            if ($pdfPath && file_exists($pdfPath)) {
                // Output the path for the parent process to capture
                $this->line($pdfPath);
                return 0;
            } else {
                $this->error("Failed to generate PDF for enrollment {$enrollmentId}");
                return 1;
            }

        } catch (\Exception $e) {
            $this->error("Error generating assessment: " . $e->getMessage());
            Log::error('Concurrent assessment generation command failed', [
                'enrollment_id' => $this->argument('enrollment_id'),
                'job_id' => $this->argument('job_id'),
                'exception' => $e,
            ]);
            return 1;
        }
    }

    /**
     * Generate assessment PDF for concurrent processing
     */
    private function generateAssessmentPdf(StudentEnrollment $enrollment, string $outputDir, string $jobId): ?string
    {
        try {
            $settingsService = app(GeneralSettingsService::class);
            
            // Override the school title to ensure correct display
            $generalSettings = $settingsService->getGlobalSettingsModel();
            if ($generalSettings) {
                $generalSettings->school_portal_title = 'Data Center College of the Philippines of Baguio City, Inc.';
            }

            // Prepare data for PDF generation
            $data = [
                'student' => $enrollment,
                'subjects' => $enrollment->SubjectsEnrolled,
                'school_year' => mb_convert_encoding(
                    $settingsService->getCurrentSchoolYearString() ?? '',
                    'UTF-8',
                    'auto'
                ),
                'semester' => mb_convert_encoding(
                    $settingsService->getAvailableSemesters()[$settingsService->getCurrentSemester()] ?? '',
                    'UTF-8',
                    'auto'
                ),
                'tuition' => $enrollment->studentTuition,
                'general_settings' => $generalSettings,
            ];

            // Generate unique filename with student info for sorting
            $studentLastName = $enrollment->student->last_name ?? 'Unknown';
            $studentFirstName = $enrollment->student->first_name ?? 'Unknown';
            $sanitizedLastName = preg_replace('/[^a-zA-Z0-9]/', '', $studentLastName);
            $sanitizedFirstName = preg_replace('/[^a-zA-Z0-9]/', '', $studentFirstName);
            
            $filename = sprintf(
                "%s_%s_%d.pdf",
                $sanitizedLastName,
                $sanitizedFirstName,
                $enrollment->id
            );
            
            // Ensure output directory exists
            if (!File::exists($outputDir)) {
                File::makeDirectory($outputDir, 0755, true);
            }

            $pdfPath = $outputDir . DIRECTORY_SEPARATOR . $filename;

            // Render HTML
            $html = view('pdf.assesment-form', $data)->render();

            // Generate PDF using BrowsershotService
            $success = BrowsershotService::generatePdf($html, $pdfPath, [
                'format' => 'A4',
                'landscape' => true,
                'print_background' => true,
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
                'timeout' => 120,
                'wait_until_network_idle' => false,
            ]);

            if ($success && file_exists($pdfPath)) {
                // Create resource record
                $enrollment->resources()->create([
                    'resourceable_id' => $enrollment->id,
                    'resourceable_type' => $enrollment::class,
                    'type' => 'assessment',
                    'file_path' => $pdfPath,
                    'file_name' => $filename,
                    'mime_type' => 'application/pdf',
                    'disk' => 'local',
                    'file_size' => filesize($pdfPath),
                    'metadata' => [
                        'school_year' => $settingsService->getCurrentSchoolYearString(),
                        'semester' => $settingsService->getAvailableSemesters()[$settingsService->getCurrentSemester()] ?? '',
                        'generation_method' => 'concurrent_process',
                        'generated_at' => now()->toISOString(),
                        'job_id' => $jobId,
                    ],
                ]);

                return $pdfPath;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('PDF generation failed in concurrent command', [
                'enrollment_id' => $enrollment->id,
                'job_id' => $jobId,
                'exception' => $e,
            ]);
            throw $e;
        }
    }
}
