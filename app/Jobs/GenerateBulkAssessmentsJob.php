<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Models\StudentEnrollment;
use App\Services\GeneralSettingsService;
use App\Services\BrowsershotService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\DB;

use App\Models\User;

final class GenerateBulkAssessmentsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 1800; // 30 minutes timeout for bulk operations
    public int $tries = 2;

    private string $jobId;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private array $filters,
        private int $userId,
        ?string $jobId = null
    ) {
        $this->jobId = $jobId ?? uniqid('bulk_assessment_', true);
        
        // Use default queue for bulk operations
        $this->onQueue('default');

        Log::info('GenerateBulkAssessmentsJob created', [
            'job_id' => $this->jobId,
            'filters' => $this->filters,
            'user_id' => $this->userId,
        ]);
    }

    /**
     * Execute the job using Laravel concurrency for optimal performance.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting concurrent bulk assessment generation', [
                'job_id' => $this->jobId,
                'filters' => $this->filters,
            ]);

            // Get filtered and sorted enrollments
            $enrollments = $this->getFilteredAndSortedEnrollments();

            if ($enrollments->isEmpty()) {
                $this->sendNotification(
                    'No Enrollments Found',
                    'No enrollments match the selected criteria.',
                    'warning'
                );
                return;
            }

            Log::info('Found enrollments for concurrent generation', [
                'job_id' => $this->jobId,
                'count' => $enrollments->count(),
            ]);

            // Generate assessments concurrently and compile into single PDF
            $pdfPath = $this->generateAssessmentsConcurrently($enrollments);

            if ($pdfPath && file_exists($pdfPath)) {
                $this->sendSuccessNotification($pdfPath, $enrollments->count());
            } else {
                throw new \Exception('Failed to generate concurrent assessment PDF');
            }

        } catch (\Exception $e) {
            Log::error('Concurrent bulk assessment generation failed', [
                'job_id' => $this->jobId,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            $this->sendNotification(
                'Assessment Generation Failed',
                'Error: ' . $e->getMessage(),
                'danger'
            );

            throw $e;
        }
    }

    /**
     * Get filtered enrollments sorted alphabetically by student last name
     */
    private function getFilteredAndSortedEnrollments()
    {
        $settingsService = app(GeneralSettingsService::class);
        $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
        $currentSemester = $settingsService->getCurrentSemester();

        $query = StudentEnrollment::where('school_year', $currentSchoolYear)
            ->where('semester', $currentSemester)
            ->where('status', 'Verified By Cashier')
            ->with(['student', 'course', 'subjectsEnrolled.subject', 'studentTuition']);

        // Include deleted records if requested
        if ($this->filters['include_deleted'] ?? true) {
            $query->withTrashed();
        }

        // Apply course filter with proper type casting
        if (isset($this->filters['course_filter']) && $this->filters['course_filter'] !== 'all') {
            $query->whereExists(function ($subQuery) {
                $subQuery->select(DB::raw(1))
                    ->from('courses')
                    ->whereRaw('CAST(student_enrollment.course_id AS BIGINT) = courses.id')
                    ->where('courses.code', 'LIKE', $this->filters['course_filter'] . '%');
            });
        }

        // Apply year level filter
        if (isset($this->filters['year_level_filter']) && $this->filters['year_level_filter'] !== 'all') {
            $query->where('academic_year', $this->filters['year_level_filter']);
        }

        // Apply student limit
        if (isset($this->filters['student_limit']) && $this->filters['student_limit'] !== 'all') {
            $query->limit((int) $this->filters['student_limit']);
        }

        // Get enrollments and sort by student last name alphabetically
        $enrollments = $query->get();
        
        return $enrollments->sortBy(function ($enrollment) {
            return $enrollment->student->last_name ?? '';
        })->values(); // Reset array keys after sorting
    }

    /**
     * Generate assessments concurrently using Laravel's concurrency features
     */
    private function generateAssessmentsConcurrently($enrollments): ?string
    {
        try {
            $totalEnrollments = $enrollments->count();
            $batchSize = min(10, max(2, (int) ceil($totalEnrollments / 10))); // Optimal batch size
            $batches = $enrollments->chunk($batchSize);

            Log::info('Starting concurrent assessment generation', [
                'job_id' => $this->jobId,
                'total_enrollments' => $totalEnrollments,
                'batch_size' => $batchSize,
                'total_batches' => $batches->count(),
            ]);

            // Create temporary directory for individual PDFs
            $tempDir = storage_path('app/temp/assessments_' . $this->jobId);
            if (!File::exists($tempDir)) {
                File::makeDirectory($tempDir, 0755, true);
            }

            $allGeneratedPaths = [];

            // Process batches concurrently
            foreach ($batches as $batchIndex => $batch) {
                Log::info("Processing batch {$batchIndex} concurrently", [
                    'job_id' => $this->jobId,
                    'batch_size' => $batch->count(),
                ]);

                // Use direct concurrent processing for better reliability
                $batchResults = [];
                foreach ($batch as $enrollment) {
                    Log::info('Processing enrollment concurrently', [
                        'job_id' => $this->jobId,
                        'enrollment_id' => $enrollment->id,
                    ]);

                    $pdfPath = $this->generateIndividualAssessmentConcurrently(
                        $enrollment->id,
                        $tempDir
                    );

                    if ($pdfPath) {
                        $batchResults[] = (object) [
                            'successful' => true,
                            'output' => $pdfPath,
                            'errorOutput' => '',
                            'exitCode' => 0,
                        ];
                    } else {
                        $batchResults[] = (object) [
                            'successful' => false,
                            'output' => '',
                            'errorOutput' => 'Failed to generate PDF',
                            'exitCode' => 1,
                        ];
                    }
                }

                // Collect successful results from direct processing
                foreach ($batchResults as $index => $result) {
                    Log::info('Direct processing result', [
                        'job_id' => $this->jobId,
                        'batch_index' => $batchIndex,
                        'process_index' => $index,
                        'successful' => $result->successful,
                        'exit_code' => $result->exitCode,
                        'output' => $result->output,
                        'error_output' => $result->errorOutput,
                    ]);

                    if ($result->successful) {
                        $outputPath = trim($result->output);
                        if ($outputPath && file_exists($outputPath)) {
                            $allGeneratedPaths[] = $outputPath;
                            Log::info('Successfully added PDF path', [
                                'job_id' => $this->jobId,
                                'path' => $outputPath,
                                'file_size' => filesize($outputPath),
                            ]);
                        } else {
                            Log::warning('Process successful but file not found', [
                                'job_id' => $this->jobId,
                                'expected_path' => $outputPath,
                                'file_exists' => $outputPath ? file_exists($outputPath) : false,
                            ]);
                        }
                    } else {
                        Log::error('Direct processing failed in batch', [
                            'job_id' => $this->jobId,
                            'batch_index' => $batchIndex,
                            'process_index' => $index,
                            'exit_code' => $result->exitCode,
                            'error' => $result->errorOutput,
                            'output' => $result->output,
                        ]);
                    }
                }

                // Small delay between batches to prevent overwhelming the system
                if ($batchIndex < $batches->count() - 1) {
                    usleep(100000); // 0.1 seconds
                }
            }

            Log::info('Concurrent generation completed', [
                'job_id' => $this->jobId,
                'generated_count' => count($allGeneratedPaths),
                'expected_count' => $totalEnrollments,
            ]);

            // Compile all PDFs into one sorted PDF
            $compiledPath = $this->compilePdfsIntoSortedDocument($allGeneratedPaths);

            // Clean up temporary directory
            File::deleteDirectory($tempDir);

            return $compiledPath;

        } catch (\Exception $e) {
            Log::error('Concurrent assessment generation failed', [
                'job_id' => $this->jobId,
                'exception' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Compile individual PDFs into a single sorted document
     */
    private function compilePdfsIntoSortedDocument(array $pdfPaths): ?string
    {
        try {
            $compiledPath = storage_path('app/public/bulk_assessments_' . date('Y-m-d_H-i-s') . '.pdf');

            // Sort PDF paths alphabetically by filename (which includes last name)
            sort($pdfPaths);

            Log::info('Compiling sorted PDFs', [
                'job_id' => $this->jobId,
                'pdf_count' => count($pdfPaths),
                'compiled_path' => $compiledPath,
            ]);

            // Get enrollment IDs from PDF filenames and regenerate combined HTML
            $enrollmentIds = $this->extractEnrollmentIdsFromPaths($pdfPaths);
            $sortedEnrollments = $this->getSortedEnrollmentsByIds($enrollmentIds);
            $combinedHtml = $this->generateCombinedAssessmentHtml($sortedEnrollments);

            // Generate final compiled PDF
            $success = BrowsershotService::generatePdf($combinedHtml, $compiledPath, [
                'format' => 'A4',
                'landscape' => true,
                'print_background' => true,
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
                'timeout' => 600, // 10 minutes for compilation
                'wait_until_network_idle' => false,
            ]);

            if (!$success || !file_exists($compiledPath)) {
                throw new \Exception('Failed to compile sorted assessment PDF');
            }

            Log::info('Sorted assessment PDF compiled successfully', [
                'job_id' => $this->jobId,
                'path' => $compiledPath,
                'file_size' => filesize($compiledPath),
            ]);

            return $compiledPath;

        } catch (\Exception $e) {
            Log::error('PDF compilation failed', [
                'job_id' => $this->jobId,
                'exception' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Build artisan command for generating individual assessment
     */
    private function buildAssessmentCommand(int $enrollmentId, string $tempDir): string
    {
        $artisanPath = base_path('artisan');
        return "php {$artisanPath} assessment:generate-concurrent {$enrollmentId} {$tempDir} {$this->jobId}";
    }

    /**
     * Generate individual assessment concurrently using direct method call
     */
    private function generateIndividualAssessmentConcurrently(int $enrollmentId, string $tempDir): ?string
    {
        try {
            // Find the enrollment record (including soft-deleted)
            $enrollment = StudentEnrollment::withTrashed()
                ->with(['student', 'course', 'subjectsEnrolled.subject', 'studentTuition'])
                ->find($enrollmentId);

            if (!$enrollment) {
                Log::warning("Enrollment {$enrollmentId} not found for concurrent generation");
                return null;
            }

            $settingsService = app(GeneralSettingsService::class);

            // Override the school title to ensure correct display
            $generalSettings = $settingsService->getGlobalSettingsModel();
            if ($generalSettings) {
                $generalSettings->school_portal_title = 'Data Center College of the Philippines of Baguio City, Inc.';
            }

            // Prepare data for PDF generation
            $data = [
                'student' => $enrollment,
                'subjects' => $enrollment->SubjectsEnrolled,
                'school_year' => mb_convert_encoding(
                    $settingsService->getCurrentSchoolYearString() ?? '',
                    'UTF-8',
                    'auto'
                ),
                'semester' => mb_convert_encoding(
                    $settingsService->getAvailableSemesters()[$settingsService->getCurrentSemester()] ?? '',
                    'UTF-8',
                    'auto'
                ),
                'tuition' => $enrollment->studentTuition,
                'general_settings' => $generalSettings,
            ];

            // Generate unique filename with student info for sorting
            $studentLastName = $enrollment->student->last_name ?? 'Unknown';
            $studentFirstName = $enrollment->student->first_name ?? 'Unknown';
            $sanitizedLastName = preg_replace('/[^a-zA-Z0-9]/', '', $studentLastName);
            $sanitizedFirstName = preg_replace('/[^a-zA-Z0-9]/', '', $studentFirstName);

            $filename = sprintf(
                "%s_%s_%d.pdf",
                $sanitizedLastName,
                $sanitizedFirstName,
                $enrollment->id
            );

            $pdfPath = $tempDir . DIRECTORY_SEPARATOR . $filename;

            // Render HTML
            $html = view('pdf.assesment-form', $data)->render();

            // Generate PDF using BrowsershotService
            $success = BrowsershotService::generatePdf($html, $pdfPath, [
                'format' => 'A4',
                'landscape' => true,
                'print_background' => true,
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
                'timeout' => 120,
                'wait_until_network_idle' => false,
            ]);

            if ($success && file_exists($pdfPath)) {
                return $pdfPath;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('Concurrent individual assessment generation failed', [
                'job_id' => $this->jobId,
                'enrollment_id' => $enrollmentId,
                'exception' => $e->getMessage(),
            ]);
            return null;
        }
    }

    /**
     * Extract enrollment IDs from PDF file paths
     */
    private function extractEnrollmentIdsFromPaths(array $pdfPaths): array
    {
        $enrollmentIds = [];
        foreach ($pdfPaths as $path) {
            // Extract enrollment ID from filename pattern: LastName_FirstName_ID.pdf
            if (preg_match('/_(\d+)\.pdf$/', basename($path), $matches)) {
                $enrollmentIds[] = (int) $matches[1];
            }
        }
        return $enrollmentIds;
    }

    /**
     * Get sorted enrollments by IDs maintaining the order from PDF paths
     */
    private function getSortedEnrollmentsByIds(array $enrollmentIds): \Illuminate\Support\Collection
    {
        return StudentEnrollment::withTrashed()
            ->whereIn('id', $enrollmentIds)
            ->with(['student', 'course', 'subjectsEnrolled.subject', 'studentTuition'])
            ->get()
            ->sortBy(function ($enrollment) {
                return $enrollment->student->last_name ?? '';
            })
            ->values();
    }

    /**
     * Generate combined HTML for all assessments with corrected school title
     */
    private function generateCombinedAssessmentHtml($enrollments): string
    {
        $settingsService = app(GeneralSettingsService::class);

        $combinedHtml = '<!DOCTYPE html><html><head><meta charset="UTF-8"><style>
            .page-break { page-break-before: always; }
            .first-page { page-break-before: auto; }
        </style></head><body>';

        foreach ($enrollments as $index => $enrollment) {
            // Override the school title to ensure correct display
            $generalSettings = $settingsService->getGlobalSettingsModel();
            if ($generalSettings) {
                $generalSettings->school_portal_title = 'Data Center College of the Philippines of Baguio City, Inc.';
            }

            $data = [
                'student' => $enrollment,
                'subjects' => $enrollment->SubjectsEnrolled,
                'school_year' => mb_convert_encoding(
                    $settingsService->getCurrentSchoolYearString() ?? '',
                    'UTF-8',
                    'auto'
                ),
                'semester' => mb_convert_encoding(
                    $settingsService->getAvailableSemesters()[$settingsService->getCurrentSemester()] ?? '',
                    'UTF-8',
                    'auto'
                ),
                'tuition' => $enrollment->studentTuition,
                'general_settings' => $generalSettings,
            ];

            $pageClass = $index === 0 ? 'first-page' : 'page-break';
            $assessmentHtml = view('pdf.assesment-form', $data)->render();

            // Remove DOCTYPE and html/body tags from individual assessments
            $assessmentHtml = preg_replace('/<!DOCTYPE[^>]*>/', '', $assessmentHtml);
            $assessmentHtml = preg_replace('/<\/?html[^>]*>/', '', $assessmentHtml);
            $assessmentHtml = preg_replace('/<\/?head[^>]*>/', '', $assessmentHtml);
            $assessmentHtml = preg_replace('/<\/?body[^>]*>/', '', $assessmentHtml);

            $combinedHtml .= "<div class=\"{$pageClass}\">{$assessmentHtml}</div>";
        }

        $combinedHtml .= '</body></html>';
        return $combinedHtml;
    }

    /**
     * Send success notification with download link
     */
    private function sendSuccessNotification(string $pdfPath, int $count): void
    {
        $publicPath = str_replace(storage_path('app/public/'), '', $pdfPath);
        $downloadUrl = Storage::url($publicPath);

        $this->sendNotification(
            'Assessment Generation Complete',
            "Successfully generated {$count} assessments (sorted alphabetically by last name).",
            'success',
            $downloadUrl
        );
    }

    /**
     * Send notification to the user who initiated the job
     */
    private function sendNotification(string $title, string $body, string $type, ?string $downloadUrl = null): void
    {
        try {
            $user = User::find($this->userId);
            if (!$user) {
                Log::warning('User not found for notification', [
                    'job_id' => $this->jobId,
                    'user_id' => $this->userId,
                ]);
                return;
            }

            // Create notification data for database storage
            $notificationData = [
                'title' => $title,
                'body' => $body,
                'type' => $type,
                'actions' => $downloadUrl ? [
                    [
                        'label' => 'Download PDF',
                        'url' => $downloadUrl,
                        'openUrlInNewTab' => true,
                        'icon' => 'heroicon-o-arrow-down-tray',
                    ]
                ] : [],
                'persistent' => true,
                'job_id' => $this->jobId,
            ];

            // Store notification in database directly to avoid Filament context issues
            $user->notifications()->create([
                'id' => \Illuminate\Support\Str::uuid(),
                'type' => 'App\\Notifications\\BulkAssessmentNotification',
                'data' => $notificationData,
                'read_at' => null,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            Log::info('Notification sent successfully', [
                'job_id' => $this->jobId,
                'user_id' => $this->userId,
                'title' => $title,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to send notification', [
                'job_id' => $this->jobId,
                'user_id' => $this->userId,
                'exception' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
        }
    }
}
