<?php

declare(strict_types=1);

namespace App\Jobs;

use App\Models\StudentEnrollment;
use App\Services\GeneralSettingsService;
use App\Services\BrowsershotService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\File;

final class GenerateIndividualAssessmentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public int $timeout = 300; // 5 minutes per individual assessment
    public int $tries = 2;

    /**
     * Create a new job instance.
     */
    public function __construct(
        private int $enrollmentId,
        private string $outputDirectory,
        private string $jobId
    ) {
        // Use high-priority queue for individual assessments
        $this->onQueue('assessments');
    }

    /**
     * Execute the job.
     */
    public function handle(): string
    {
        try {
            Log::info('Starting individual assessment generation', [
                'job_id' => $this->jobId,
                'enrollment_id' => $this->enrollmentId,
            ]);

            // Find the enrollment record (including soft-deleted)
            $enrollment = StudentEnrollment::withTrashed()
                ->with(['student', 'course', 'subjectsEnrolled.subject', 'studentTuition'])
                ->find($this->enrollmentId);

            if (!$enrollment) {
                throw new \Exception("Enrollment with ID {$this->enrollmentId} not found");
            }

            // Generate the assessment PDF
            $pdfPath = $this->generateAssessmentPdf($enrollment);

            if (!$pdfPath || !file_exists($pdfPath)) {
                throw new \Exception("Failed to generate PDF for enrollment {$this->enrollmentId}");
            }

            Log::info('Individual assessment generated successfully', [
                'job_id' => $this->jobId,
                'enrollment_id' => $this->enrollmentId,
                'pdf_path' => $pdfPath,
                'file_size' => filesize($pdfPath),
            ]);

            return $pdfPath;

        } catch (\Exception $e) {
            Log::error('Individual assessment generation failed', [
                'job_id' => $this->jobId,
                'enrollment_id' => $this->enrollmentId,
                'exception' => $e->getMessage(),
            ]);
            throw $e;
        }
    }

    /**
     * Generate assessment PDF for individual enrollment
     */
    private function generateAssessmentPdf(StudentEnrollment $enrollment): ?string
    {
        try {
            $settingsService = app(GeneralSettingsService::class);
            
            // Override the school title to ensure correct display
            $generalSettings = $settingsService->getGlobalSettingsModel();
            if ($generalSettings) {
                $generalSettings->school_portal_title = 'Data Center College of the Philippines of Baguio City, Inc.';
            }

            // Prepare data for PDF generation
            $data = [
                'student' => $enrollment,
                'subjects' => $enrollment->SubjectsEnrolled,
                'school_year' => mb_convert_encoding(
                    $settingsService->getCurrentSchoolYearString() ?? '',
                    'UTF-8',
                    'auto'
                ),
                'semester' => mb_convert_encoding(
                    $settingsService->getAvailableSemesters()[$settingsService->getCurrentSemester()] ?? '',
                    'UTF-8',
                    'auto'
                ),
                'tuition' => $enrollment->studentTuition,
                'general_settings' => $generalSettings,
            ];

            // Generate unique filename with student info for sorting
            $studentLastName = $enrollment->student->last_name ?? 'Unknown';
            $studentFirstName = $enrollment->student->first_name ?? 'Unknown';
            $sanitizedLastName = preg_replace('/[^a-zA-Z0-9]/', '', $studentLastName);
            $sanitizedFirstName = preg_replace('/[^a-zA-Z0-9]/', '', $studentFirstName);
            
            $filename = sprintf(
                "%s_%s_%d.pdf",
                $sanitizedLastName,
                $sanitizedFirstName,
                $enrollment->id
            );
            
            // Ensure output directory exists
            if (!File::exists($this->outputDirectory)) {
                File::makeDirectory($this->outputDirectory, 0755, true);
            }

            $pdfPath = $this->outputDirectory . DIRECTORY_SEPARATOR . $filename;

            // Render HTML
            $html = view('pdf.assesment-form', $data)->render();

            // Generate PDF using BrowsershotService
            $success = BrowsershotService::generatePdf($html, $pdfPath, [
                'format' => 'A4',
                'landscape' => true,
                'print_background' => true,
                'margin_top' => 10,
                'margin_bottom' => 10,
                'margin_left' => 10,
                'margin_right' => 10,
                'timeout' => 120,
                'wait_until_network_idle' => false,
            ]);

            if ($success && file_exists($pdfPath)) {
                // Create resource record
                $enrollment->resources()->create([
                    'resourceable_id' => $enrollment->id,
                    'resourceable_type' => $enrollment::class,
                    'type' => 'assessment',
                    'file_path' => $pdfPath,
                    'file_name' => $filename,
                    'mime_type' => 'application/pdf',
                    'disk' => 'local',
                    'file_size' => filesize($pdfPath),
                    'metadata' => [
                        'school_year' => $settingsService->getCurrentSchoolYearString(),
                        'semester' => $settingsService->getAvailableSemesters()[$settingsService->getCurrentSemester()] ?? '',
                        'generation_method' => 'concurrent_bulk',
                        'generated_at' => now()->toISOString(),
                        'job_id' => $this->jobId,
                    ],
                ]);

                return $pdfPath;
            }

            return null;

        } catch (\Exception $e) {
            Log::error('PDF generation failed for individual assessment', [
                'job_id' => $this->jobId,
                'enrollment_id' => $enrollment->id,
                'exception' => $e->getMessage(),
            ]);
            throw $e;
        }
    }
}
