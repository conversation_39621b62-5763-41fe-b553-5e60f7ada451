<?php

declare(strict_types=1);

namespace App\Filament\Pages;

use App\Services\StudentReportingService;
use App\Services\GeneralSettingsService;
use App\Models\StudentEnrollment;
use App\Jobs\GenerateBulkAssessmentsJob;
use Filament\Actions\Action;
use Filament\Forms\Components\Select;
use Filament\Forms\Components\Checkbox;
use Filament\Forms\Components\Section;
use Filament\Forms\Components\Grid;
use Filament\Notifications\Notification;
use Filament\Pages\Page;
use Filament\Support\Enums\MaxWidth;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\DB;

class StudentReportingPage extends Page
{
    protected static ?string $navigationIcon = 'heroicon-o-chart-bar';

    protected static ?string $navigationLabel = 'Student Analytics';

    protected static ?string $title = 'Student Analytics Dashboard';

    protected static ?string $navigationGroup = 'Reports';

    protected static ?int $navigationSort = 1;

    protected static string $view = 'filament.pages.student-reporting-page';

    public array $reportData = [];

    protected ?StudentReportingService $reportingService = null;

    public function mount(): void
    {
        try {
            $this->reportingService = app(StudentReportingService::class);

            // Load report data
            $this->loadReportData();
        } catch (\Exception $e) {
            // If there's an error, set empty report data
            $this->reportData = [];

            Notification::make()
                ->title('Error Loading Data')
                ->body('Unable to load report data: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    public function loadReportData(): void
    {
        if (!$this->reportingService) {
            $this->reportingService = app(StudentReportingService::class);
        }

        $this->reportData = $this->reportingService->generateDashboardReport();
    }

    protected function getHeaderActions(): array
    {
        return [
            Action::make('generateAllAssessments')
                ->label('Generate All Assessments')
                ->icon('heroicon-o-document-duplicate')
                ->color('primary')
                ->form([
                    Section::make('Assessment Generation Options')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    Select::make('course_filter')
                                        ->label('Course Filter')
                                        ->options([
                                            'all' => 'All Courses',
                                            'BSIT' => 'BSIT Only',
                                            'BSBA' => 'BSBA Only',
                                            'BSHM' => 'BSHM Only',
                                        ])
                                        ->default('all')
                                        ->required(),

                                    Select::make('year_level_filter')
                                        ->label('Year Level Filter')
                                        ->options([
                                            'all' => 'All Year Levels',
                                            '1' => '1st Year Only',
                                            '2' => '2nd Year Only',
                                            '3' => '3rd Year Only',
                                            '4' => '4th Year Only',
                                        ])
                                        ->default('all')
                                        ->required(),
                                ]),

                            Grid::make(2)
                                ->schema([
                                    Select::make('student_limit')
                                        ->label('Number of Students')
                                        ->options([
                                            'all' => 'All Students',
                                            '10' => 'First 10 Students',
                                            '25' => 'First 25 Students',
                                            '50' => 'First 50 Students',
                                            '100' => 'First 100 Students',
                                        ])
                                        ->default('all')
                                        ->required()
                                        ->helperText('Limit the number of assessments to generate for testing purposes.'),

                                    Checkbox::make('include_deleted')
                                        ->label('Include Deleted Records')
                                        ->default(true)
                                        ->helperText('Include soft-deleted enrollment records in the generation process.'),
                                ]),
                        ])
                ])
                ->action(function (array $data): void {
                    $this->handleConcurrentAssessmentGeneration($data);
                }),

            Action::make('exportData')
                ->label('Export Data')
                ->icon('heroicon-o-document-arrow-down')
                ->color('success')
                ->form([
                    Section::make('Export Options')
                        ->schema([
                            Grid::make(2)
                                ->schema([
                                    Select::make('format')
                                        ->label('Export Format')
                                        ->options([
                                            'csv' => 'Excel (CSV)',
                                            'pdf' => 'PDF Document',
                                        ])
                                        ->default('csv')
                                        ->required(),

                                    Select::make('course_filter')
                                        ->label('Course Filter')
                                        ->options([
                                            'all' => 'All Courses',
                                            'BSIT' => 'BSIT Only',
                                            'BSBA' => 'BSBA Only',
                                            'BSHM' => 'BSHM Only',
                                        ])
                                        ->default('all')
                                        ->required(),
                                ]),

                            Grid::make(2)
                                ->schema([
                                    Select::make('year_level_filter')
                                        ->label('Year Level Filter')
                                        ->options([
                                            'all' => 'All Year Levels',
                                            '1' => '1st Year Only',
                                            '2' => '2nd Year Only',
                                            '3' => '3rd Year Only',
                                            '4' => '4th Year Only',
                                        ])
                                        ->default('all')
                                        ->required(),

                                    Checkbox::make('preview_mode')
                                        ->label('Preview Only (First 10 Records)')
                                        ->default(false),
                                ]),
                        ])
                ])
                ->action(function (array $data): void {
                    $this->handleExport($data);
                }),

            Action::make('refreshData')
                ->label('Refresh Data')
                ->icon('heroicon-o-arrow-path')
                ->action(function (): void {
                    $this->loadReportData();
                }),

            Action::make('restartQueue')
                ->label('Restart Horizon')
                ->icon('heroicon-o-arrow-path')
                ->color('warning')
                ->requiresConfirmation()
                ->modalHeading('Restart Horizon Queue System')
                ->modalDescription('This will restart Horizon and all queue workers. Use this if jobs are not processing or timing out.')
                ->action(function (): void {
                    try {
                        // Terminate current Horizon processes
                        \Illuminate\Support\Facades\Artisan::call('horizon:terminate');

                        // Wait a moment for processes to terminate
                        sleep(2);

                        // Start Horizon again (this will be handled by the process manager)
                        \Illuminate\Support\Facades\Artisan::call('horizon');

                        Notification::make()
                            ->title('Horizon Restarted')
                            ->body('Horizon queue system has been restarted with updated configuration.')
                            ->success()
                            ->send();
                    } catch (\Exception $e) {
                        Notification::make()
                            ->title('Horizon Restart Failed')
                            ->body('Failed to restart Horizon: ' . $e->getMessage())
                            ->danger()
                            ->send();
                    }
                }),
        ];
    }

    /**
     * Handle concurrent assessment generation using queue
     */
    public function handleConcurrentAssessmentGeneration(array $data): void
    {
        try {
            $userId = Auth::id();
            if (!$userId) {
                Notification::make()
                    ->title('Authentication Required')
                    ->body('You must be logged in to generate assessments.')
                    ->danger()
                    ->send();
                return;
            }

            // Dispatch job to queue for processing
            $jobId = uniqid('bulk_assessment_', true);
            GenerateBulkAssessmentsJob::dispatch($data, $userId, $jobId);

            // Get preview count for user feedback
            $previewCount = $this->getFilteredEnrollmentsCount($data);

            Notification::make()
                ->title('Assessment Generation Queued')
                ->body("Queued generation of {$previewCount} assessments (sorted alphabetically by last name). You will receive a notification when complete.")
                ->info()
                ->persistent()
                ->send();

            Log::info('Bulk assessment generation job dispatched', [
                'job_id' => $jobId,
                'user_id' => $userId,
                'filters' => $data,
                'estimated_count' => $previewCount,
            ]);

        } catch (\Exception $e) {
            Log::error('Failed to queue assessment generation: ' . $e->getMessage(), [
                'exception' => $e,
                'data' => $data,
            ]);

            Notification::make()
                ->title('Queue Failed')
                ->body('Failed to queue assessment generation: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    /**
     * Get count of filtered enrollments for preview
     */
    private function getFilteredEnrollmentsCount(array $data): int
    {
        try {
            $settingsService = app(GeneralSettingsService::class);
            $currentSchoolYear = $settingsService->getCurrentSchoolYearString();
            $currentSemester = $settingsService->getCurrentSemester();

            $query = StudentEnrollment::where('school_year', $currentSchoolYear)
                ->where('semester', $currentSemester)
                ->where('status', 'Verified By Cashier');

            // Include deleted records if requested
            if ($data['include_deleted'] ?? true) {
                $query->withTrashed();
            }

            // Apply course filter with proper type casting
            if (isset($data['course_filter']) && $data['course_filter'] !== 'all') {
                $query->whereExists(function ($subQuery) use ($data) {
                    $subQuery->select(DB::raw(1))
                        ->from('courses')
                        ->whereRaw('CAST(student_enrollment.course_id AS BIGINT) = courses.id')
                        ->where('courses.code', 'LIKE', $data['course_filter'] . '%');
                });
            }

            // Apply year level filter
            if (isset($data['year_level_filter']) && $data['year_level_filter'] !== 'all') {
                $query->where('academic_year', $data['year_level_filter']);
            }

            // Apply student limit for count estimation
            if (isset($data['student_limit']) && $data['student_limit'] !== 'all') {
                return min($query->count(), (int) $data['student_limit']);
            }

            return $query->count();

        } catch (\Exception $e) {
            Log::error('Failed to get enrollment count: ' . $e->getMessage());
            return 0;
        }
    }

    /**
     * Handle the export action
     */
    public function handleExport(array $data): void
    {
        try {
            // Ensure we have the reporting service
            if (!$this->reportingService) {
                $this->reportingService = app(StudentReportingService::class);
            }

            // Prepare filters from form data
            $filters = [
                'course_filter' => $data['course_filter'] ?? 'all',
                'year_level_filter' => $data['year_level_filter'] ?? 'all',
                'preview_mode' => $data['preview_mode'] ?? false,
            ];

            // Handle preview mode
            if (isset($data['preview_mode']) && $data['preview_mode']) {
                // Generate preview data
                $preview = $this->reportingService->generateExportPreview($filters);

                // Store in session for display
                session(['export_preview' => $preview]);

                // Notify user
                Notification::make()
                    ->title('Export Preview Generated')
                    ->body('Preview shows ' . count($preview['students']) . ' of ' . $preview['total_count'] . ' total records.')
                    ->info()
                    ->send();
            } else {
                // Queue the actual export job
                $format = $data['format'] ?? 'csv';
                $userId = Auth::id() ?? 1; // Fallback to admin user if not authenticated

                $this->reportingService->queueExport($filters, $format, $userId);

                // Notify user
                Notification::make()
                    ->title('Export Queued Successfully')
                    ->body('Your export has been queued for processing. You will receive a notification when it\'s ready.')
                    ->success()
                    ->send();
            }
        } catch (\Exception $e) {
            // Log the error
            Log::error('Export failed: ' . $e->getMessage(), [
                'exception' => $e,
                'data' => $data,
            ]);

            // Notify user
            Notification::make()
                ->title('Export Failed')
                ->body('Failed to process export: ' . $e->getMessage())
                ->danger()
                ->send();
        }
    }

    // public static function canAccess(): bool
    // {
    //     return auth()->user()->can('view_student_reports') || auth()->user()->hasRole('admin');
    // }





    public function getMaxContentWidth(): MaxWidth
    {
        return MaxWidth::Full;
    }
}
